import React, { useState, useRef } from 'react';
import { View, StyleSheet, Dimensions, SafeAreaView } from 'react-native';
import PagerView from 'react-native-pager-view';
import { useNavigation, useRoute } from '@react-navigation/native';
import {
  showSnackbar,
  ComponentStatus,
  FBottomSheet,
  showBottomSheet,
  FLoading,
} from 'wini-mobile-components';
import TitleWithBackAction from '../../Layout/titleWithBackAction';
import StepIndicator from '../../../components/StepIndicator';
import Step1TransferInfo from './Step1TransferInfo';
import Step2OTPVerification from './Step2OTPVerification';
import Step3TransactionDetail from './Step3TransactionDetail';
import CustomerBottomSheet from '../../../components/CustomerBottomSheet';
import { ColorThemes } from '../../../assets/skin/colors';
import { useSelectorCustomerState } from '../../../redux/hook/customerHook';
import HeaderShop from '../../../components/shop/HeaderShop';

import { DataController } from '../../../base/baseController';
import { randomGID, Ultis } from '../../../utils/Utils';
import { TransactionStatus, TransactionType } from '../../../Config/Contanst';
import { InforHeader } from '../../Layout/headers/inforHeader';
import { CustomerActions } from '../../../redux/reducers/CustomerReducer';

const { width } = Dimensions.get('window');

const TransferCANPointScreen: React.FC = () => {
  const navigation = useNavigation();
  const pagerRef = useRef<PagerView>(null);
  const customerBottomSheetRef = useRef<any>(null);
  const customer = useSelectorCustomerState().data;
  //router param
  const route = useRoute<any>();
  const type = route?.params?.Type;
  // State management
  const [currentStep, setCurrentStep] = useState(1);
  const [transferAmount, setTransferAmount] = useState('');
  const [recipientName, setRecipientName] = useState('');
  const [recipientPhone, setRecipientPhone] = useState('');
  const [recipientId, setRecipientId] = useState('');
  const [otpValue, setOtpValue] = useState('');
  const [isVerifying, setIsVerifying] = useState(false);
  const [transactionData, setTransactionData] = useState<any>(null);

  // Mock current points - replace with actual data
  const [currentPoints, setCurrentPoints] = useState(0);
  const [loading, setLoading] = useState(false);
  const fetchCurrentPoints = async () => {
    // TODO: call API get current points
    if (!customer?.Id) return;
    //lấy từ history reward
    setLoading(true);
    const controller = new DataController('HistoryReward');
    const res = await controller.getListSimple({
      query: `@CustomerId: {${customer?.Id}} ((@Status: [${TransactionStatus.success}] @Value: [0 +inf]) | (@Status: [${TransactionStatus.pending} ${TransactionStatus.success}] @Value: [-inf 0]))`,
      sortby: { BY: 'DateCreated', DIRECTION: 'DESC' },
    });
    if (res.code === 200 && res.data.length) {
      var total = res.data.reduce(
        (total: number, item: any) => total + item.Value,
        0,
      );
      setCurrentPoints(total);
    }
    setLoading(false);
  };

  React.useEffect(() => {
    fetchCurrentPoints();
  }, []);

  const handleBack = () => {
    if (currentStep > 1) {
      const prevStep = currentStep - 1;
      setCurrentStep(prevStep);
      pagerRef.current?.setPage(prevStep - 1);
    } else {
      navigation.goBack();
    }
  };

  const handleSelectRecipient = () => {
    showBottomSheet({
      ref: customerBottomSheetRef,
      children: (
        <CustomerBottomSheet
          ref={customerBottomSheetRef}
          onSelectCustomer={customer => {
            setRecipientId(customer.Id);
            setRecipientName(customer.Name);
            setRecipientPhone(customer.Mobile || '');
          }}
        />
      ),
    });
  };

  const handleStep1Next = () => {
    setCurrentStep(2);
    pagerRef.current?.setPage(1);

    // TODO: call API send otp
  };

  const handleOTPComplete = (otp: string) => {
    setOtpValue(otp);
  };

  const handleResendOTP = () => {
    setOtpValue('');
    showSnackbar({
      message: 'Đã gửi lại mã OTP',
      status: ComponentStatus.SUCCSESS,
    });
  };

  const handleVerifyOTP = async () => {
    setIsVerifying(true);
    // TODO: call API verify otp
    const result = await CustomerActions.verify2Action(customer.Id, otpValue);
    if (result.code !== 200) {
      setIsVerifying(false);
      showSnackbar({
        message: 'Mã xác thực không chính xác',
        status: ComponentStatus.ERROR,
      });
      return;
    }
    // Mock OTP verification
    //TODO: call API verify otp
    var lstData = [];
    var dataSend = {
      Id: randomGID(),
      CustomerId: customer?.Id,
      Name: customer?.Name,
      Mobile: customer?.Mobile,
      DateCreated: new Date().getTime(),
      Status:
        type === TransactionType.tranfer
          ? TransactionStatus.success
          : TransactionStatus.pending,
      Value: 0 - parseInt(transferAmount),
      Type: type,
      Description:
        type === TransactionType.tranfer
          ? `Chuyển ${Ultis.money(
            transferAmount,
          )} CAN Point cho ${recipientName}`
          : `Rút ${Ultis.money(transferAmount)} CAN Point`,
      CustomerRevice: recipientId,
      Code: Ultis.randomString(12),
    };
    lstData.push(dataSend);
    if (type === TransactionType.tranfer) {
      var dataRecive = {
        ...dataSend,
        Id: randomGID(),
        CustomerId: recipientId,
        Name: recipientName,
        CustomerRevice: customer?.Id,
        Value: parseInt(transferAmount),
        Description: `Nhận ${Ultis.money(transferAmount)} CAN Point từ ${customer?.Name
          }`,
        Code: Ultis.randomString(12),
      };
      lstData.push(dataRecive);
    }
    const controller = new DataController('HistoryReward');
    const res = await controller.add(lstData);
    if (res.code === 200) {
      setIsVerifying(false);
      var transactionData = {
        status:
          dataSend.Status === TransactionStatus.success
            ? 'success'
            : dataSend.Status === TransactionStatus.pending
              ? 'pending'
              : 'failed',
        transactionId: dataSend.Code,
        amount: parseInt(transferAmount),
        recipientName,
        recipientPhone,
        senderName: customer?.Name || '',
        timestamp: new Date().toISOString(),
        type: type,
      };
      setTransactionData(transactionData);
      setCurrentStep(3);
      pagerRef.current?.setPage(2);
      showSnackbar({
        message:
          type === TransactionType.tranfer
            ? 'Chuyển CAN Point thành công!'
            : 'Rút CAN Point thành công!',
        status: ComponentStatus.SUCCSESS,
      });
    } else {
      setIsVerifying(false);
      showSnackbar({
        message:
          type === TransactionType.tranfer
            ? 'Chuyển CAN Point thất bại!'
            : 'Rút CAN Point thất bại!',
        status: ComponentStatus.ERROR,
      });
    }
  };

  const handleDone = () => {
    navigation.goBack();
  };

  return (
    <SafeAreaView style={styles.container}>
      <InforHeader
        title={
          type === TransactionType.tranfer
            ? 'Chuyển CAN Point'
            : 'Rút CAN Point'
        }
      />
      {loading && <FLoading visible={loading} />}
      <View style={styles.container}>
        <FBottomSheet ref={customerBottomSheetRef} />
        <StepIndicator currentStep={currentStep} totalSteps={3} />

        <PagerView
          ref={pagerRef}
          style={styles.pagerView}
          initialPage={0}
          scrollEnabled={false}>
          <View key="step1" style={styles.pageContainer}>
            <Step1TransferInfo
              currentPoints={currentPoints}
              transferAmount={transferAmount}
              recipientName={recipientName}
              recipientPhone={recipientPhone}
              onAmountChange={setTransferAmount}
              onSelectRecipient={handleSelectRecipient}
              onNext={handleStep1Next}
              type={type}
            />
          </View>

          <View key="step2" style={styles.pageContainer}>
            <Step2OTPVerification
              phoneNumber={recipientPhone}
              isVerifying={isVerifying}
              onOTPComplete={handleOTPComplete}
              onResendOTP={handleResendOTP}
              onVerify={handleVerifyOTP}
              otpValue={otpValue}
            />
          </View>

          <View key="step3" style={styles.pageContainer}>
            {transactionData && (
              <Step3TransactionDetail
                transactionData={transactionData}
                onDone={handleDone}
              />
            )}
          </View>
        </PagerView>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  pagerView: {
    flex: 1,
  },
  pageContainer: {
    flex: 1,
    width: width,
  },
});

export default TransferCANPointScreen;
