import { useNavigation } from '@react-navigation/native';
import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { TextField, Winicon } from 'wini-mobile-components';
import { RootScreen } from '../../router/router';
import { Title, TypeMenuShop } from '../../Config/Contanst';
import { ScrollView } from 'react-native-gesture-handler';
import LeaderShopInfo from '../Field/LeaderShopInfo';
import { OrderData } from '../../mock/shopData';
import { useDispatch, useSelector } from 'react-redux';
import { OrderActions } from '../../redux/reducers/OrderReducer';
import { useSelectorShopState } from '../../redux/hook/shopHook ';
import { useSelectorOrderState } from '../../redux/hook/orderHook ';
import { useSelectorCustomerState } from '../../redux/hook/customerHook';
import { ColorThemes } from '../../assets/skin/colors';
import { TypoSkin } from '../../assets/skin/typography';
import MenuHaveShop from '../Field/menu/MenuHaveShop/MenuHaveShop';
import MenuCards from '../Field/menu/MenuHaveShop/MenuCards';
import iconSvg from '../../svg/icon';
import { fetchCategories } from '../../redux/actions/categoryAction';
import { RootState } from '../../redux/store/store';
import { OrderDA } from '../../modules/order/orderDA';
import ShopDA from '../../modules/shop/da';

interface HaveShopProps {
  shop: any[];
}
const HaveShop = (props: HaveShopProps) => {
  const navigation = useNavigation<any>();
  const [getNewOrder, SetGetNewOrder] = useState<number>(0);
  const [getProcessingOrder, SetGetProcessingOrder] = useState<number>(0);
  const [getCancelOrder, SetGetCancelOrder] = useState<number>(0);
  const dispatch = useDispatch<any>();
  const shopInfo = useSelectorShopState().data;
  const { data: OrderData } = useSelector((state: RootState) => state.order);
  let [shopMoney, setShopMoney] = useState<any>(null);
  let shopDA = new ShopDA()


  const getShopMoney = async () => {
    await shopDA.getShopFinancialSummary(shopInfo[0]?.Id).then((res) => {
      if (res?.code === 200) {
        setShopMoney(res?.data);
      }
    })
  }
  useEffect(() => {
    dispatch(OrderActions.getInforOrder(shopInfo[0]?.Id));
    dispatch(fetchCategories());
    getShopMoney()
  }, []);
  useEffect(() => {
    //focus effect
    const unsubscribe = navigation.addListener('focus', async () => {
      dispatch(OrderActions.getInforOrder(shopInfo[0]?.Id));
    });
    return unsubscribe;
  }, [navigation]);

  useEffect(() => {
    SetGetNewOrder(OrderData?.NewOrder?.number);
    SetGetProcessingOrder(OrderData?.ProcessOrder?.number);
    SetGetCancelOrder(OrderData?.CancelOrder?.number);
  }, [OrderData]);
  return (
    <ScrollView style={{ flex: 1 }}>
      <View style={styles.container}>
        <View style={styles.navBar}>
          <MenuHaveShop
            svgIcon={iconSvg.walletAction}
            title="Đơn hàng mới"
            getNewOrder={getNewOrder}
            order={RootScreen.OrderDetail}
            type={Title.New}
            status={1}
            numberOrder={getNewOrder ? getNewOrder : 0}
          />
          <MenuHaveShop
            svgIcon={iconSvg.deliveryIcon}
            title="Đang xử lý"
            getNewOrder={getProcessingOrder}
            order={RootScreen.OrderDetail}
            type={Title.Processing}
            status={1}
            numberOrder={getProcessingOrder ? getProcessingOrder : 0}
          />
          <MenuHaveShop
            svgIcon={iconSvg.done}
            title="Hoàn thành"
            order={RootScreen.OrderDetail}
            type={Title.Done}
            status={1}
          />
          <MenuHaveShop
            svgIcon={iconSvg.cancel}
            title="Hủy/hoàn"
            getNewOrder={getCancelOrder}
            order={RootScreen.OrderDetail}
            type={Title.Cancel}
            status={1}
            numberOrder={getCancelOrder ? getCancelOrder : 0}
          />
          <MenuHaveShop
            svgIcon={iconSvg.star}
            title="Đánh giá"
            order={RootScreen.Review}
            type={Title.Shop}
          />
        </View>
        <View
          style={{
            height: 63,
            backgroundColor: ColorThemes.light.primary_background,
            borderRadius: 10,
            marginTop: 20,
            borderWidth: 0.3,
            borderColor: '#1890FF4D',
          }}>
          <Text
            style={{
              fontSize: 16,
              color: ColorThemes.light.neutral_text_title_color,
              fontWeight: '700',
              marginBottom: 3,
            }}>
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                display: 'flex',
                alignItems: 'center',
                width: '100%',
              }}>
              <View style={{ padding: 12 }}>
                <Text
                  style={{
                    fontSize: 16,
                    color: ColorThemes.light.neutral_text_title_color,
                    fontWeight: '700',
                    paddingTop: 8,
                  }}>
                  Số dư bán hàng
                </Text>
                <Text
                  style={{
                    ...TypoSkin.title4,
                    color: ColorThemes.light.neutral_text_title_color,
                    paddingTop: 8,
                  }}>
                  {shopMoney?.totalOrderAmount ?
                    Number(shopMoney.totalOrderAmount).toLocaleString('vi-VN', {
                      minimumFractionDigits: 0,
                      maximumFractionDigits: 0
                    }) : 0} VNĐ
                </Text>
              </View>
              <View>
                <TouchableOpacity
                  style={{
                    backgroundColor: ColorThemes.light.primary_main_color,
                    borderRadius: 5,
                    width: 64,
                    height: 24,
                    justifyContent: 'center',
                    alignItems: 'center',
                    marginTop: 20,
                    marginRight: 10,
                    marginBottom: 29,
                  }}
                  onPress={() => navigation.navigate(RootScreen.WithDrawMoney)}
                >
                  <Text
                    style={{
                      color:
                        ColorThemes.light.neutral_absolute_background_color,
                    }}>
                    Rút tiền
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </Text>
        </View>
        <View style={{ height: '100%' }}>
          <View style={{ display: 'flex', flexDirection: 'column', gap: 12 }}>
            <LeaderShopInfo shop={props.shop} />
            <MenuCards
              svgIcon={iconSvg.manageProduct}
              color="orange"
              title="QL sản phẩm"
              order={RootScreen.ManageProduct}
            />
            <MenuCards
              svgIcon={iconSvg.manageReport}
              color="orange"
              title="Báo cáo"
              order={RootScreen.ChartReport}
            />
            <MenuCards
              svgIcon={iconSvg.promotion}
              color="orange"
              title="Khuyến mại"
              order={RootScreen.ShopPromortion}
            />
            <MenuCards
              svgIcon={iconSvg.promotion}
              color="orange"
              title="Cấu hình afiliate"
              order={RootScreen.ConfigAffiliate}
            />
          </View>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    paddingHorizontal: 16,
  },
  navBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 6,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    marginBottom: 10,
    width: '100%',
    marginVertical: 10,
  },
  navBadge: {
    position: 'absolute',
    top: 5,
    right: 4,
    backgroundColor: '#FF0000',
    color: '#fff',
    fontSize: 10,
    paddingHorizontal: 4,
    borderRadius: 10,
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 8,
    margin: 10,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  cardTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
  },
  cardHeaderLeft: {
    display: 'flex',
    flexDirection: 'row',
    alignContent: 'center',
    gap: 3,
    flex: 1,
  },
  cardHeaderRight: {},

  cardContent: {
    marginTop: 10,
  },
  infoLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  infoValue: {
    fontSize: 14,
    color: '#000',
    fontWeight: '500',
  },
  infoValueEdit: {
    lineHeight: 14,
    display: 'flex',
    alignItems: 'center',
    color: '#000',
    fontWeight: '500',
  },
  actionCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 8,
    margin: 10,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    height: 70,
  },
  actionText: {
    ...TypoSkin.body3,
    color: ColorThemes.light.neutral_text_title_color,
    marginLeft: 10,
  },

  input: {
    flex: 1,
    fontSize: 16,
    color: 'black',
    borderWidth: 0,
    backgroundColor: 'white',
    lineHeight: 14,
  },
});

export default HaveShop;
