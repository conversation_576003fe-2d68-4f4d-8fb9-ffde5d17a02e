/* eslint-disable react-native/no-inline-styles */
import React, { forwardRef, useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  ScrollView,
  Image,
  Pressable,
  Dimensions,
  KeyboardAvoidingView,
  Platform,
  FlatList,
  Modal,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { TypoSkin } from '../../../assets/skin/typography';
import {
  AppSvg,
  Checkbox,
  ComponentStatus,
  FBottomSheet,
  hideBottomSheet,
  showBottomSheet,
  showSnackbar,
  showDialog,
  FDialog,
} from 'wini-mobile-components';
import iconSvg from '../../../svg/icon';
import { ColorThemes } from '../../../assets/skin/colors';
import ModalPromotion from './ModalPromotion';
import WScreenFooter from '../../../Screen/Layout/footer';
import { AppDispatch, RootState } from '../../../redux/store/store';
import { useDispatch, useSelector } from 'react-redux';
import ScreenHeader from '../../../Screen/Layout/header';
import { TextInput } from 'react-native-paper';
import { fetchCategories } from '../../../redux/actions/categoryAction';
import CartPromotion from './Cart/CartPromotion';
import { useSelectorShopState } from '../../../redux/hook/shopHook ';
import ConfigAPI from '../../../Config/ConfigAPI';
import { DataController } from '../../../base/baseController';
import EmptyPage from '../../../Screen/emptyPage';

const ShopPromortionComponent = () => {
  const popupRef = useRef<any>(null);
  const dialogRef = useRef<any>(null);
  const [activeButton, setActiveButton] = useState<string>('Tất cả');
  const [productPromotion, setProductPromotion] = useState<any[]>([]);
  const [isShow, setIsShow] = useState<boolean>(false);
  const [getSelectProduct, setGetSelectProduct] = useState<string>('');
  const [discountValue, setDiscountValue] = useState<string>('');
  const [dataDiscount, setDataDiscount] = useState<any[]>([]);
  const [allCategoryIds, setAllCategoryIds] = useState<any[]>([]);
  const [selectedProducts, setSelectedProducts] = useState<string[]>([]);
  const [selectAll, setSelectAll] = useState<boolean>(false);
  const dispatch = useDispatch<any>();
  const productDA = new DataController('Product');
  const shopInfo = useSelectorShopState().data;
  const { data: dataCategory } = useSelector(
    (state: RootState) => state.category,
  );
  const handleShow = () => {
    setIsShow(true);
  };

  const closeModal = () => {
    setIsShow(false);
  };



  const getAllCategoryIds = (dataCategory: any) => {
    let ids: any[] = [];
    dataCategory.forEach((cat: any) => {
      // Luôn thêm ID của category cha
      ids.push({
        Id: cat.Id,
        Name: cat.Name,
      });

      if (cat.Children && cat.Children.length > 0) {
        // Nếu có Children, lấy Id của các Children
        ids = ids.concat(
          cat.Children.map((child: any) => {
            return {
              Id: child.Id,
              Name: child.Name,
            };
          }),
        );
      }
    });
    setAllCategoryIds(ids);
  };

  const handleEditPromotion = (item: any) => {
    setGetSelectProduct(item?.Id);
  };

  const handleCancelEditPromotion = () => {
    setGetSelectProduct('');
  };

  const getInforProductPromotion = async (CategoryId?: string) => {
    try {
      let response;
      if (CategoryId) {
        response = await productDA.getListSimple({
          query: `@ShopId:{${shopInfo[0]?.Id}} @CategoryId:{${CategoryId}}`,
        });
      } else {
        response = await productDA.getListSimple({
          query: `@ShopId:{${shopInfo[0]?.Id}}`,
        });
      }

      if (response?.code === 200) {
        if (response?.data && response?.data?.length > 0) {
          let filterData = response?.data?.filter((item: any) => item?.Discount);
          setProductPromotion(filterData);
        } else {
          setProductPromotion([]);
        }
      } else {
        console.error('Failed to fetch promotion products:', response?.message);
        setProductPromotion([]);
        showSnackbar({
          message: response?.message || 'Không thể tải danh sách sản phẩm khuyến mãi',
          status: ComponentStatus.ERROR,
        });
      }
    } catch (error) {
      console.error('Error fetching promotion products:', error);
      setProductPromotion([]);
      showSnackbar({
        message: 'Có lỗi xảy ra khi tải danh sách sản phẩm',
        status: ComponentStatus.ERROR,
      });
    }
  };

  const editPromotion = async (item: any) => {
    try {
      const discountNumber = Number(discountValue);

      // Validate discount value
      if (isNaN(discountNumber) || discountNumber < 0 || discountNumber > 100) {
        showSnackbar({
          message: 'Giá trị khuyến mãi phải từ 0 đến 100%',
          status: ComponentStatus.ERROR,
        });
        return;
      }

      const response = await productDA.edit([
        { ...item, Discount: discountNumber },
      ]);

      if (response?.code === 200) {
        await getInforProductPromotion();
        setGetSelectProduct('');
        setDiscountValue('');
        showSnackbar({
          message: 'Cập nhật khuyến mãi thành công',
          status: ComponentStatus.SUCCSESS,
        });
      } else {
        showSnackbar({
          message: response?.message || 'Có lỗi xảy ra khi cập nhật khuyến mãi',
          status: ComponentStatus.ERROR,
        });
      }
    } catch (error) {
      console.error('Error editing promotion:', error);
      showSnackbar({
        message: 'Không thể kết nối đến máy chủ. Vui lòng thử lại sau.',
        status: ComponentStatus.ERROR,
      });
    }
  };

  const deletePromotion = async (item: any) => {
    showDialog({
      ref: dialogRef,
      status: ComponentStatus.WARNING,
      title: 'Bạn chắc chắn xoá khuyến mãi này?',
      onSubmit: async () => {
        try {
          // Hiển thị loading nếu cần
          showSnackbar({
            message: 'Đang xóa khuyến mãi...',
            status: ComponentStatus.INFOR,
          });

          let response = await productDA.edit([{ ...item, Discount: 0 }]);

          if (response?.code === 200) {
            // Cập nhật danh sách sản phẩm
            await getInforProductPromotion();
            setGetSelectProduct('');

            // Thông báo thành công
            showSnackbar({
              message: 'Xóa khuyến mãi thành công',
              status: ComponentStatus.SUCCSESS,
            });
          } else {
            // Xử lý lỗi từ API
            showSnackbar({
              message: response?.message || 'Có lỗi xảy ra khi xóa khuyến mãi',
              status: ComponentStatus.ERROR,
            });
          }
        } catch (error) {
          console.error('Error deleting promotion:', error);
          // Xử lý lỗi network hoặc lỗi khác
          showSnackbar({
            message: 'Không thể kết nối đến máy chủ. Vui lòng thử lại sau.',
            status: ComponentStatus.ERROR,
          });
        }
      },
    });

  };

  useEffect(() => {
    dispatch(fetchCategories());
    if (shopInfo && shopInfo.length > 0) {
      getInforProductPromotion();
    }
  }, [shopInfo]);

  useEffect(() => {
    if (dataCategory && dataCategory.length > 0) {
      getAllCategoryIds(dataCategory);
    }
  }, [dataCategory]);

  useEffect(() => {
    if (isShow === false) {
      // Refresh data when modal closes
      getInforProductPromotion();
    }
  }, [isShow]);

  // Cập nhật trạng thái selectAll khi selectedProducts thay đổi
  useEffect(() => {
    if (productPromotion.length > 0) {
      const allSelected = productPromotion.every(product =>
        selectedProducts.includes(product.Id),
      );
      setSelectAll(allSelected);
    }
  }, [selectedProducts, productPromotion]);

  const handleSelectAllData = (ref: any) => {
    hideBottomSheet(ref as any);
  };
  const handleSelectMenu = async (type: string, item?: any) => {
    console.log('check-item', item);
    setActiveButton(type);
    if (!item) {
      await getInforProductPromotion();
    } else {
      await getInforProductPromotion(item?.Id);
    }
  };

  // Hàm xử lý chọn/bỏ chọn một sản phẩm
  const handleSelectProduct = (productId: string) => {
    setSelectedProducts(prev => {
      if (prev.includes(productId)) {
        return prev.filter(id => id !== productId);
      } else {
        return [...prev, productId];
      }
    });
  };

  // Hàm xử lý chọn tất cả sản phẩm
  const handleSelectAllProducts = () => {
    if (selectAll) {
      setSelectedProducts([]);
      setSelectAll(false);
    } else {
      const allProductIds = productPromotion.map(product => product.Id);
      setSelectedProducts(allProductIds);
      setSelectAll(true);
    }
  };

  // Hàm lấy danh sách sản phẩm đã chọn
  const getSelectedProductsData = () => {
    return productPromotion.filter(product =>
      selectedProducts.includes(product.Id),
    );
  };

  // Hàm xử lý xóa các sản phẩm đã chọn
  const handleDeleteSelectedProducts = async () => {
    console.log('check-selectedProducts', selectedProducts);
    if (selectedProducts.length === 0) {
      showSnackbar({ message: 'Vui lòng chọn sản phẩm cần xóa' });
      return;
    }

    showDialog({
      ref: dialogRef,
      status: ComponentStatus.WARNING,
      title: 'Bạn chắc chắn xoá khuyến mãi này?',
      onSubmit: async () => {
        try {
          // Hiển thị loading nếu cần
          showSnackbar({
            message: 'Đang xóa khuyến mãi...',
            status: ComponentStatus.INFOR,
          });

          const selectedProductsData = getSelectedProductsData();
          let arrayData: any[] = [];
          for (let product of selectedProductsData) {
            arrayData.push({ ...product, Discount: 0 });
          }

          let response = await productDA.edit(arrayData);
          if (response?.code === 200) {
            // Cập nhật danh sách sản phẩm
            await getInforProductPromotion();
            setSelectedProducts([]);
            setSelectAll(false);

            // Thông báo thành công
            showSnackbar({
              message: 'Xóa khuyến mãi thành công',
              status: ComponentStatus.SUCCSESS,
            });
          } else {
            // Xử lý lỗi từ API
            showSnackbar({
              message: response?.message || 'Có lỗi xảy ra khi xóa khuyến mãi',
              status: ComponentStatus.ERROR,
            });
          }
        } catch (error) {
          console.error('Error deleting promotion:', error);
          // Xử lý lỗi network hoặc lỗi khác
          showSnackbar({
            message: 'Không thể kết nối đến máy chủ. Vui lòng thử lại sau.',
            status: ComponentStatus.ERROR,
          });
        }
      },
    });
  };

  // Hàm xử lý cập nhật khuyến mãi cho các sản phẩm đã chọn
  const handleUpdateSelectedProducts = () => {
    if (selectedProducts.length === 0) {
      showSnackbar({ message: 'Vui lòng chọn sản phẩm cần cập nhật' });
      return;
    }

    const selectedProductsData = getSelectedProductsData();
    setDataDiscount(selectedProductsData);
    handleShow();
  };

  return (
    <View style={styles.content}>
      <FDialog ref={dialogRef} />
      <FBottomSheet ref={popupRef} />
      <View style={styles.PromotionMenu}>
        <Text style={styles.label}>Danh sách</Text>
        <TouchableOpacity
          style={styles.deleteButton}
          onPress={() => handleDeleteSelectedProducts()}>
          <AppSvg SvgSrc={iconSvg.delete} size={12} />
          <Text style={styles.buttonText}>Xóa hàng loạt</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.updateButton}
          onPress={() => handleUpdateSelectedProducts()}>
          <AppSvg SvgSrc={iconSvg.updateAll} size={12} />
          <Text style={styles.buttonTextSuccess}>Cập nhật hàng loạt</Text>
        </TouchableOpacity>
      </View>
      <View style={{ flexDirection: 'row', alignItems: 'center' }}>
        <TouchableOpacity
          style={
            activeButton === 'Tất cả' ? styles.activeButton : styles.button
          }
          onPress={() => handleSelectMenu('Tất cả')}>
          <Text style={styles.buttonTextMenu}>
            Tất cả ({productPromotion?.length})
          </Text>
        </TouchableOpacity>
        <FlatList
          data={allCategoryIds}
          style={{ flex: 1 }}
          keyExtractor={(item, i) => `${i} ${item.Id}`}
          horizontal={true}
          renderItem={({ item, index }) => (
            <View style={styles.PromorionNabar} key={`item-${index}`}>
              <TouchableOpacity
                style={
                  activeButton === item?.Name
                    ? styles.activeButton
                    : styles.button
                }
                onPress={() => handleSelectMenu(item?.Name, item)}>
                <Text style={styles.buttonTextMenu}>{item?.Name}</Text>
              </TouchableOpacity>
            </View>
          )}
        />
      </View>
      <Pressable style={styles.menuDetail}>
        <View style={styles.header}>
          <Text style={styles.headerText}>
            <Checkbox
              value={selectAll}
              onChange={() => handleSelectAllProducts()}
              size={24}
            />
          </Text>
          <Text style={styles.headerText}>Ảnh</Text>
          <Text style={styles.headerText}>
            Sản phẩm <AppSvg SvgSrc={iconSvg.dropDown} size={16} />
          </Text>
          <Text style={styles.headerText}>
            KM <AppSvg SvgSrc={iconSvg.dropDown} size={16} />
          </Text>
          <Text style={styles.headerText}>
            TT <AppSvg SvgSrc={iconSvg.dropDown} size={16} />
          </Text>
        </View>
        {productPromotion && productPromotion?.length > 0 ? (
          <FlatList
            data={productPromotion}
            style={{ flex: 1, height: '100%' }}
            ListFooterComponent={() => <View style={{ height: 100 }} />}
            keyExtractor={(item, i) => `${i} ${item.Id}`}
            renderItem={({ item, index }) => (
              <Pressable style={styles.row} key={`item-${index}`}>
                <View style={{}}>
                  <Checkbox
                    value={selectedProducts.includes(item.Id)}
                    onChange={() => handleSelectProduct(item.Id)}
                    size={24}
                  />
                </View>
                <View style={styles.imageContainer}>
                  <Image
                    source={{
                      uri: ConfigAPI.urlImg + item?.Img,
                    }} // Thay bằng URL ảnh thực tế
                    style={styles.image}
                  />
                </View>
                <Text style={styles.text}>{item?.Name}</Text>
                {getSelectProduct !== item?.Id ? (
                  <Text style={styles.textTwo}>
                    {item?.Discount ? item?.Discount : 0}%
                  </Text>
                ) : (
                  <TextInput
                    style={{
                      width: 60,
                      height: 35,
                      marginRight: 20,
                      backgroundColor: '#f8f9fa',
                      color: 'black',
                      borderWidth: 1,
                      borderColor: '#e0e0e0',
                      paddingHorizontal: 8,
                      paddingVertical: 6,
                      textAlign: 'center',
                      fontSize: 14,
                      fontWeight: '500',
                    }}
                    value={item?.Discount.toString()}
                    defaultValue={item?.Discount.toString()}
                    onBlur={e => setDiscountValue(e.nativeEvent.text)}
                    keyboardType="numeric"
                    placeholder="0"
                    placeholderTextColor="#999"
                  />
                )}

                {getSelectProduct !== item?.Id ? (
                  <View style={styles.actions}>
                    <TouchableOpacity onPress={() => handleEditPromotion(item)}>
                      <AppSvg SvgSrc={iconSvg.editPromotion} size={24} />
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => deletePromotion(item)}>
                      <AppSvg SvgSrc={iconSvg.deletePromotion} size={24} />
                    </TouchableOpacity>
                  </View>
                ) : (
                  <View style={styles.actions}>
                    <TouchableOpacity onPress={() => editPromotion(item)}>
                      <AppSvg SvgSrc={iconSvg.confirm} size={24} />
                    </TouchableOpacity>
                    <TouchableOpacity
                      onPress={() => handleCancelEditPromotion()}>
                      <AppSvg SvgSrc={iconSvg.cancelEdit} size={24} />
                    </TouchableOpacity>
                  </View>
                )}
              </Pressable>
            )}
          />
        ) : (
          <View
            style={{
              flex: 1,
              alignItems: 'center',
              justifyContent: 'center',
              paddingBottom: 100,
            }}>
            <EmptyPage />
          </View>
        )}
      </Pressable>
      <WScreenFooter style={{ width: '100%', paddingHorizontal: 20 }}>
        <TouchableOpacity
          style={styles.buyButton}
          onPress={() => {
            showBottomSheet({
              ref: popupRef,
              enableDismiss: true,
              children: (
                <BottomSheetPromotion
                  ref={popupRef}
                  handleShow={() => handleShow()}
                  handleGetDataDiscount={value => setDataDiscount(value)}
                  handleSelectAllData={ref => hideBottomSheet(ref as any)}
                />
              ),
            });
          }}>
          <Text style={styles.actionButtonText}>Thêm sản phẩm khuyến mại</Text>
        </TouchableOpacity>
      </WScreenFooter>
      <View style={{ zIndex: 1000, position: 'absolute', top: 0, left: 0 }}>
        <ModalPromotion
          isShow={isShow}
          closeModal={closeModal}
          svgSrc={iconSvg.updateAll}
          title="Cập nhật khuyến mãi hàng loạt"
          dataDiscount={dataDiscount}
          ref={popupRef}
          handleSelectAllData={handleSelectAllData}
        />

      </View>

      {/* Modal xác nhận xóa hàng loạt */}

    </View>
  );
};

const BottomSheetPromotion = forwardRef<
  typeof FBottomSheet,
  {
    handleShow: (data?: any) => void;
    handleGetDataDiscount: (data: any[]) => void;
    handleSelectAllData: (ref: any) => void;
  }
>((props, ref) => {
  const { data } = useSelector((state: RootState) => state.category);
  const [dataCategory, setDataCategory] = useState<any[]>([]);
  const [selectTwo, setSelectTwo] = useState<boolean>(false);
  const [selectThree, setSelectThree] = useState<boolean>(false);
  const [dataDiscount, setDataDiscount] = useState<any[]>([]);
  const [selectAll, setSelectAll] = useState<boolean>(false);
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());

  const navigation = useNavigation();
  const productDA = new DataController('Product');
  const shopInfo = useSelectorShopState().data;

  useEffect(() => {
    if (data && data?.length > 0) {
      setDataCategory(data);
    }
  }, [data]);

  // Hàm helper để lấy sản phẩm theo CategoryId
  const getProductsByCategoryId = async (categoryId: string) => {
    try {
      const response = await productDA.getListSimple({
        query: `@ShopId:{${shopInfo[0]?.Id}} @CategoryId:{${categoryId}}`,
      });

      if (response?.code === 200 && response?.data) {
        // Filter products without discount
        return response.data.filter((item: any) => !item?.Discount);
      }
      return [];
    } catch (error) {
      console.error('Error getting products by category:', error);
      return [];
    }
  };

  // hàm chọn danh mục dữ liệu ở mục 1
  const handleSelectCategory = async (item: any) => {
    if (item && item?.Children && item?.Children?.length > 0) {
      setDataCategory(item?.Children);
    } else {
      await getProductByCategory(item?.Id);
    }
  };

  const handleSelectDiscount = () => {
    props.handleShow();
  };

  const getProductByCategory = async (CategoryId: any) => {
    setSelectTwo(true);
    setSelectThree(true);

    try {
      const productMap = new Map(); // Sử dụng Map để tránh duplicate hiệu quả hơn

      // Lấy sản phẩm từ danh mục được chọn
      const categoryProducts = await getProductsByCategoryId(CategoryId);
      categoryProducts.forEach((product: any) => {
        productMap.set(product.Id, product);
      });

      // Tìm danh mục cha để lấy thông tin children
      const parentCategory = dataCategory.find(cat => cat.Id === CategoryId);
      if (
        parentCategory &&
        parentCategory.Children &&
        parentCategory.Children.length > 0
      ) {
        // Lấy sản phẩm từ tất cả danh mục con
        for (const child of parentCategory.Children) {
          const childProducts = await getProductsByCategoryId(child.Id);
          childProducts.forEach((product: any) => {
            productMap.set(product.Id, product);
          });
        }
      }

      const uniqueProducts = Array.from(productMap.values());

      if (uniqueProducts.length > 0) {
        setDataCategory(uniqueProducts);
        props.handleGetDataDiscount(uniqueProducts);
      } else {
        setDataCategory([]);
        props.handleGetDataDiscount([]);
      }
    } catch (error) {
      console.error('Error getting products by category:', error);
      setDataCategory([]);
      props.handleGetDataDiscount([]);
    }
  };

  const getdataByMenuTwo = async (item: any) => {
    if (item && item?.Id) {
      await getProductByCategory(item?.Id);
    }
  };

  // hàm chọn tất cả danh mục
  const handleSelectAllData = async () => {
    const isCurrentlySelectingAll = !selectAll;
    setSelectAll(isCurrentlySelectingAll);

    // Nếu đang bỏ chọn tất cả
    if (!isCurrentlySelectingAll) {
      setSelectedItems(new Set());
      setDataDiscount([]);
      props.handleGetDataDiscount([]);
      setSelectTwo(false);
      return;
    }

    // Nếu đang chọn tất cả
    setSelectTwo(true);
    let allProducts: any[] = [];
    const newSelectedItems = new Set<string>();

    try {
      // Xử lý từng danh mục trong dataCategory
      for (const category of dataCategory) {
        // Thêm category vào selected items
        newSelectedItems.add(category.Id);

        // Lấy sản phẩm từ danh mục cha
        const parentProducts = await getProductsByCategoryId(category.Id);
        allProducts = [...allProducts, ...parentProducts];

        // Lấy sản phẩm từ tất cả danh mục con
        if (category.Children && category.Children.length > 0) {
          for (const child of category.Children) {
            newSelectedItems.add(child.Id);
            const childProducts = await getProductsByCategoryId(child.Id);
            allProducts = [...allProducts, ...childProducts];
          }
        }
      }

      // Loại bỏ sản phẩm trùng lặp dựa trên Id
      const uniqueProducts = allProducts.filter(
        (product, index, self) =>
          index === self.findIndex(p => p.Id === product.Id),
      );

      setSelectedItems(newSelectedItems);
      setDataDiscount(uniqueProducts);
      props.handleGetDataDiscount(uniqueProducts);
    } catch (error) {
      console.error('Error selecting all categories:', error);
      // Reset states on error
      setSelectAll(false);
      setSelectTwo(false);
      setSelectedItems(new Set());
      setDataDiscount([]);
      props.handleGetDataDiscount([]);
    }
  };

  const handleGetDataDiscount = async (data: any) => {
    const newSelectedItems = new Set(selectedItems);
    const isCurrentlySelected = selectedItems.has(data.Id);


    if (data && data?.Id && data?.InStock && data?.Price) {
      props.handleGetDataDiscount([data]);
      return
    }
    // Nếu data là một sản phẩm đơn lẻ
    if (data && data?.Id && !data?.Children) {
      if (isCurrentlySelected) {
        // Bỏ chọn item
        newSelectedItems.delete(data.Id);
        const updatedDataDiscount = dataDiscount.filter(item => item.Id !== data.Id);
        setDataDiscount(updatedDataDiscount);
        setSelectedItems(newSelectedItems);
        props.handleGetDataDiscount(updatedDataDiscount);
      } else {
        // Chọn item
        newSelectedItems.add(data.Id);
        const updatedDataDiscount = [...dataDiscount, data];
        setDataDiscount(updatedDataDiscount);
        setSelectedItems(newSelectedItems);
        props.handleGetDataDiscount(updatedDataDiscount);
      }
      return;
    }

    // Nếu data là một danh mục (có thể có children)
    if (data && data?.Id) {
      if (isCurrentlySelected) {
        // Bỏ chọn danh mục và tất cả children
        newSelectedItems.delete(data.Id);
        if (data.Children && data.Children.length > 0) {
          for (const child of data.Children) {
            newSelectedItems.delete(child.Id);
          }
        }

        // Lấy tất cả sản phẩm của danh mục này để xóa khỏi dataDiscount
        let productsToRemove: any[] = [];

        // Lấy sản phẩm từ danh mục cha
        const parentProducts = await getProductsByCategoryId(data.Id);
        productsToRemove.push(...parentProducts);

        // Lấy sản phẩm từ tất cả danh mục con
        if (data.Children && data.Children.length > 0) {
          for (const child of data.Children) {
            const childProducts = await getProductsByCategoryId(child.Id);
            productsToRemove.push(...childProducts);
          }
        }

        // Xóa tất cả sản phẩm của danh mục này khỏi dataDiscount
        const productIdsToRemove = new Set(productsToRemove.map((p: any) => p.Id));
        const updatedDataDiscount = dataDiscount.filter(item => !productIdsToRemove.has(item.Id));

        setDataDiscount(updatedDataDiscount);
        setSelectedItems(newSelectedItems);
        props.handleGetDataDiscount(updatedDataDiscount);
      } else {
        // Chọn danh mục và tất cả children
        newSelectedItems.add(data.Id);

        let arrayData: any[] = [];

        // Lấy sản phẩm từ danh mục cha
        const parentProducts = await getProductsByCategoryId(data.Id);
        arrayData.push(...parentProducts);

        // Lấy sản phẩm từ tất cả danh mục con và thêm children vào selected items
        if (data.Children && data.Children.length > 0) {
          for (const child of data.Children) {
            newSelectedItems.add(child.Id);
            const childProducts = await getProductsByCategoryId(child.Id);
            arrayData.push(...childProducts);
          }
        }

        const updatedDataDiscount = [...dataDiscount, ...arrayData];
        setDataDiscount(updatedDataDiscount);
        setSelectedItems(newSelectedItems);
        props.handleGetDataDiscount(updatedDataDiscount);
      }
    }
    console.log('updatedDataDiscount', dataDiscount);
  };

  // Hàm xử lý chọn children
  const handleSelectChild = async (child: any) => {
    const newSelectedItems = new Set(selectedItems);
    const isCurrentlySelected = selectedItems.has(child.Id);

    if (isCurrentlySelected) {
      // Bỏ chọn child
      newSelectedItems.delete(child.Id);

      // Lấy sản phẩm của child để xóa khỏi dataDiscount
      const childProducts = await getProductsByCategoryId(child.Id);
      const productIdsToRemove = new Set(childProducts.map((p: any) => p.Id));
      const updatedDataDiscount = dataDiscount.filter(item => !productIdsToRemove.has(item.Id));

      setDataDiscount(updatedDataDiscount);
      setSelectedItems(newSelectedItems);
      props.handleGetDataDiscount(updatedDataDiscount);
    } else {
      // Chọn child
      newSelectedItems.add(child.Id);

      // Lấy sản phẩm của child để thêm vào dataDiscount
      const childProducts = await getProductsByCategoryId(child.Id);
      const updatedDataDiscount = [...dataDiscount, ...childProducts];

      setDataDiscount(updatedDataDiscount);
      setSelectedItems(newSelectedItems);
      props.handleGetDataDiscount(updatedDataDiscount);
    }
  };



  return (
    <Pressable
      style={{
        width: '100%',
        height: Dimensions.get('window').height - 100,
        borderTopLeftRadius: 16,
        borderTopRightRadius: 16,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
        position: 'relative',
      }}>
      <ScreenHeader
        backIcon
        title={'Chọn danh mục'}
        onBack={() => navigation.goBack()}
      />
      <KeyboardAvoidingView
        style={{ flex: 1, position: 'relative' }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'} // Adjust for iOS and Android
        keyboardVerticalOffset={Platform.OS === 'ios' ? 65 : 0} // Offset for iOS
      >
        <View
          style={{
            padding: 10,
            backgroundColor: '#fff',
          }}>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginBottom: 10,
            }}>
            <TextInput
              style={{
                flex: 1,
                backgroundColor: '#f0f0f0',
                borderRadius: 10,
                padding: 8,
                marginRight: 10,
                height: 20,
                color: 'black',
              }}
              placeholder="Search"
            />
            <TouchableOpacity>
              <Text
                style={{
                  color: '#007AFF',
                  fontSize: 16,
                }}>
                Đã chọn
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        <View>
          <TouchableOpacity
            style={{
              marginLeft: 16,
              ...TypoSkin.body3,

              borderBottomWidth: 1,
              borderBottomColor:
                ColorThemes.light.neutral_main_background_color,
              marginTop: 10,
              paddingBottom: 10,
            }}
            onPress={() => handleSelectAllData()}>
            <Text style={{ color: ColorThemes.light.primary_main_color }}>
              Chọn tất cả danh mục
            </Text>
          </TouchableOpacity>
          <Pressable style={{ height: '100%' }}>
            {dataCategory && dataCategory?.length > 0 ? (
              dataCategory?.map((item, index) => (
                <CartPromotion
                  key={`item-${index}`}
                  item={item}
                  index={index}
                  handleSelectCategory={handleSelectCategory}
                  handleSelectAllCategory={() => { }} // Empty function since not used
                  selectTwo={selectTwo}
                  getdataByMenuTwo={getdataByMenuTwo}
                  selectThree={selectThree}
                  handleGetDataDiscount={handleGetDataDiscount}
                  selectAll={selectAll}
                  selectedItems={selectedItems}
                  isSelected={selectedItems.has(item.Id)}
                  handleSelectChild={handleSelectChild}
                />
              ))
            ) : (
              <View
                style={{
                  height: '100%',
                  alignItems: 'center',
                  justifyContent: 'center',
                  paddingBottom: 250,
                }}>
                <EmptyPage />
              </View>
            )}
          </Pressable>
        </View>
        <View
          style={{
            flex: 1,
            marginBottom: 10,
            position: 'absolute',
            bottom: 2,
            width: '100%',
          }}>
          <View
            style={{
              flexDirection: 'row',
              width: '100%',
              alignItems: 'center',
              justifyContent: 'space-around',
              position: 'absolute',
              bottom: 10,
            }}>
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={() => props.handleSelectAllData(ref)}>
              <Text style={styles.buttonText}>Đóng</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.confirmButton}
              onPress={() => handleSelectDiscount()}>
              <Text style={styles.buttonTextTwo}>Xác nhận</Text>
            </TouchableOpacity>
          </View>
        </View>
      </KeyboardAvoidingView>
    </Pressable>
  );
});

const styles = StyleSheet.create({
  content: {
    flex: 1,
    alignItems: 'center',
  },
  PromotionMenu: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingLeft: 16,
    paddingRight: 16,
    paddingTop: 20,
    paddingBottom: 4,
  },
  label: {
    ...TypoSkin.title3,
    marginRight: 26,
    fontWeight: '400',
  },
  deleteButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: ColorThemes.light.error_border_color,
    paddingVertical: 5,
    paddingHorizontal: 15,
    borderRadius: 10,
    marginRight: 8,
  },
  updateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: ColorThemes.light.success_border_color,
    paddingVertical: 5,
    paddingHorizontal: 15,
    borderRadius: 10,
  },
  buttonText: {
    color: ColorThemes.light.neutral_text_subtitle_color,
    fontSize: 12,
    marginLeft: 4,
  },
  buttonTextTwo: {
    color: ColorThemes.light.neutral_absolute_background_color,
    fontSize: 12,
    marginLeft: 4,
  },
  buttonTextSuccess: {
    color: ColorThemes.light.success_main_color,
    fontSize: 12,
    marginLeft: 4,
  },
  PromorionNabar: {
    backgroundColor: '#fff',
    marginLeft: 2,
    marginTop: 10,
    marginBottom: 10,
    maxHeight: 24,
  },
  activeButton: {
    backgroundColor: ColorThemes.light.primary_background,
    paddingVertical: 5,
    paddingHorizontal: 15,
    borderRadius: 20,
    marginLeft: 16,
  },
  button: {
    backgroundColor: '#f0f0f0',
    paddingVertical: 5,
    paddingHorizontal: 15,
    borderRadius: 20,
    marginLeft: 16,
  },
  buttonTextMenu: {
    color: ColorThemes.light.primary_sub_color,
    fontSize: 12,
    fontWeight: '400',
  },
  menuDetail: {
    flex: 1,
    padding: 10,
    width: '100%',
    backgroundColor: '#fff',
    marginLeft: 12,
    marginRight: 12,
    marginBottom: 12,
    marginTop: 12,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingBottom: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#ccc',
  },
  headerText: {
    fontWeight: 'bold',
    ...TypoSkin.body2,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  imageContainer: {
    marginRight: 30,
    marginLeft: 30,
  },
  image: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  text: {
    flex: 1,
    ...TypoSkin.body3,
    marginLeft: '3%',
  },
  textTwo: {
    flex: 1,
    ...TypoSkin.body3,
    marginLeft: '15%',
  },
  actions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  editIcon: {
    fontSize: 16,
    color: '#000',
    marginRight: 10,
  },
  checkIcon: {
    fontSize: 16,
    color: '#00cc00',
    marginRight: 10,
  },
  deleteIcon: {
    fontSize: 16,
    color: '#ff0000',
  },
  checkbox: {
    marginRight: 10,
  },
  buyButton: {
    backgroundColor: 'blue',
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 50,
    borderRadius: 30,
  },

  actionButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
  cancelButton: {
    backgroundColor: '#f0f0f0', // Màu xám nhạt cho nút "Đóng"
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 5,
    borderWidth: 1,
    borderColor: '#ccc',
    width: '45%',
    alignItems: 'center',
    color: 'white',
  },
  confirmButton: {
    backgroundColor: '#007AFF', // Màu xanh cho nút "Xác nhận"
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 5,
    width: '45%',
    alignItems: 'center',
    color: 'white',
  },
  itemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    paddingLeft: 10,
    paddingRight: 20,
    paddingTop: 10,
    paddingBottom: 10,
    height: 50,
    backgroundColor: 'red',
  },
  itemText: {
    fontSize: 20,
    color: 'black',
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContainer: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 10,
    width: '80%',
    maxHeight: '80%',
    alignItems: 'center',
  },
  modalText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    textAlign: 'center',
  },
  modalSubText: {
    fontSize: 16,
    textAlign: 'center',
    color: '#666',
    marginBottom: 20,
  },
  modalButtonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
    marginTop: 20,
  },
  modalButton: {
    padding: 10,
    borderRadius: 5,
    width: '40%',
    alignItems: 'center',
  },
  modalCancelButton: {
    backgroundColor: '#f0f0f0',
    borderWidth: 1,
    borderColor: '#ccc',
  },
  modalCancelButtonText: {
    color: '#333',
    fontSize: 16,
    fontWeight: 'bold',
  },
  modalDeleteConfirmButton: {
    backgroundColor: '#ff0000',
  },
  modalDeleteConfirmButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default ShopPromortionComponent;
