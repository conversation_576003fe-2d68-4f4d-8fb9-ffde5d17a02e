import { DataController } from '../../base/baseController';
import {
  MissionType,
  StatusOrder,
  TransactionStatus,
  TransactionType,
} from '../../Config/Contanst';
import { randomGID, Ultis } from '../../utils/Utils';

class WalletDA {
  private historyRewardController: DataController;
  private missionCustomerController: DataController;
  private missionController: DataController;
  private shopRewardController: DataController;
  private shopCateController: DataController;
  private rewardController: DataController;

  constructor() {
    this.historyRewardController = new DataController('HistoryReward');
    this.missionCustomerController = new DataController('MissionCustomer');
    this.missionController = new DataController('Mission');
    this.shopRewardController = new DataController('ShopReward');
    this.shopCateController = new DataController('ShopCategory');
    this.rewardController = new DataController('Reward');
  }

  async CaculateMisson(cusId: string, type: number) {
    const [missionLogin, missionOrder] = await Promise.all([
      new DataController('Mission').getListSimple({
        page: 1,
        size: 1,
        query: `@MissonType: [${type}]`,
      }),
      new DataController('Mission').getListSimple({
        page: 1,
        size: 1,
        query: `@MissonType: [${MissionType.Order}] @Type: [2]`, // nhiệm vụ tháng
      }),
    ]);
    if (missionLogin.code === 200 && missionLogin.data.length > 0) {
      const startOfDay = new Date().setHours(0, 0, 0, 0);
      const endOfDay = new Date().setHours(23, 59, 59, 999);
      const missionCustomer =
        await this.missionCustomerController.getListSimple({
          query: `@CustomerId: {${cusId}} @MissionId: {${missionLogin.data[0].Id}} @DateCreated: [${startOfDay} ${endOfDay}] @Type: [${type}]`,
        });
      if (missionCustomer.code === 200 && missionCustomer.data.length == 0) {
        // chỉ add khi mà ngày hôm nay chưa có lịch sử đăng nhập
        await this.missionCustomerController.add([
          {
            Id: randomGID(),
            DateCreated: new Date().getTime(),
            CustomerId: cusId,
            MissionId: missionLogin.data[0].Id,
            Name: missionLogin.data[0].Name,
            Type: type,
            Value: missionLogin.data[0]?.Value,
          },
        ]);
        //#region kiểm tra điều kiện + điểm khi làm nhiệm vụ logic
        const orderController = new DataController('Order');
        //lấy ngày bắt đẩu và kết thúc của tháng
        const { start, end } = Ultis.getMonthRange();
        const order = await orderController.group({
          reducers: 'LOAD * GROUPBY 1 @CustomerId REDUCE SUM 1 @Value AS Total',
          searchRaw: `@CustomerId: {${cusId}} @DateCreated: [${start} ${end}] @Status: [${StatusOrder.success}]`,
        });
        var status = TransactionStatus.pending;
        if (order.code === 200 && order.data.length > 0) {
          const total = order.data.reduce(
            (total: number, item: any) => total + item.Total,
            0,
          );
          if (
            missionOrder.code === 200 &&
            missionOrder.data.length > 0 &&
            total >= missionOrder.data[0]?.TotalPriceOrder
          ) {
            status = TransactionStatus.success;
          }
        }
        const historyRewardController = new DataController('HistoryReward');
        await historyRewardController.add([
          {
            Id: randomGID(),
            CustomerId: cusId,
            Value: missionLogin.data[0]?.Value,
            Description: `Thưởng Nhiệm vụ {${missionLogin.data[0].Name
              }} ${Ultis.numberToTime(new Date().getTime(), true)}`,
            Type: TransactionType.mission,
            DateCreated: new Date().getTime(),
            Code: Ultis.randomString(12),
            Status: status,
          },
        ]);
        //#endregion
      }
    }

    return null;
  }

  //lấy % hoa hồng theo shop
  async getPercentRewardByShop(ShopId: string, categoryIds: string[]) {
    try {
      const result = [
        {
          categoryId: null,
          f0: 0,
          f1: 0,
          f2: 0,
        },
      ];
      categoryIds.map(async (item: any) => {
        const response = await this.shopCateController.getListSimple({
          query: `@ShopId: {${ShopId}} @CategoryId: {${item}}`,
        });
        if (response?.code === 200 && response?.data) {
          result.push({
            categoryId: item,
            f0:
              response.data.find((item: any) => item.Filial === 0)?.Percent ||
              0,
            f1:
              response.data.find((item: any) => item.Filial === 1)?.Percent ||
              0,
            f2:
              response.data.find((item: any) => item.Filial === 2)?.Percent ||
              0,
          });
        } else {
          //lấy từ ShopReward
          const response = await this.shopRewardController.getListSimple({
            query: `@ShopId: {${ShopId}}`,
          });
          if (response?.code === 200 && response?.data) {
            result.push({
              categoryId: item,
              f0:
                response.data.find((item: any) => item.Filial === 0)
                  ?.Percent || 0,
              f1:
                response.data.find((item: any) => item.Filial === 1)
                  ?.Percent || 0,
              f2:
                response.data.find((item: any) => item.Filial === 2)
                  ?.Percent || 0,
            });
          } else {
            //lấy từ Reward
            const response = await this.rewardController.getListSimple({
              query: `*`,
            });
            if (response?.code === 200 && response?.data) {
              result.push({
                categoryId: item,
                f0:
                  response.data.find((item: any) => item.Filial === 0)
                    ?.Percent || 0,
                f1:
                  response.data.find((item: any) => item.Filial === 1)
                    ?.Percent || 0,
                f2:
                  response.data.find((item: any) => item.Filial === 2)
                    ?.Percent || 0,
              });
            }
          }
        }
        return result;
      });
    } catch (error) {
      console.error('Error getting percent reward by shop:', error);
      return [];
    }
  }
}

export default WalletDA;
