import React from 'react';
import { View, StyleSheet } from 'react-native';
import { AppButton } from 'wini-mobile-components';
import { ColorThemes } from '../../../assets/skin/colors';
import OTPInputComponent from '../../../components/OTPInputComponent';

interface Step2OTPVerificationProps {
  phoneNumber?: string;
  isVerifying: boolean;
  onOTPComplete: (otp: string) => void;
  onResendOTP: () => void;
  onVerify: () => void;
  otpValue: string;
}

const Step2OTPVerificationMoney: React.FC<Step2OTPVerificationProps> = ({
  phoneNumber,
  isVerifying,
  onOTPComplete,
  onResendOTP,
  onVerify,
  otpValue,
}) => {
  const isOTPComplete = otpValue.length === 6;

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <OTPInputComponent
          length={6}
          onComplete={onOTPComplete}
          phoneNumber={phoneNumber}
          onResend={onResendOTP}
        />
      </View>

      <View style={styles.buttonContainer}>
        <AppButton
          title="Xác nhận"
          onPress={onVerify}
          disabled={!isOTPComplete || isVerifying}
          // loading={isVerifying}
          backgroundColor={
            isOTPComplete && !isVerifying
              ? ColorThemes.light.primary_main_color
              : ColorThemes.light.neutral_lighter_border_color
          }
          // titleColor={ColorThemes.light.white}
          containerStyle={styles.button}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
  },
  buttonContainer: {
    padding: 16,
    backgroundColor: ColorThemes.light.white,
    borderTopWidth: 1,
    borderTopColor: ColorThemes.light.neutral_lighter_border_color,
  },
  button: {
    borderRadius: 24,
    height: 48,
  },
});

export default Step2OTPVerificationMoney;
