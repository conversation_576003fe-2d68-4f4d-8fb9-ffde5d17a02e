import React, { useEffect, useState } from 'react';
import { View, StyleSheet, ScrollView, TextInput, TouchableOpacity } from 'react-native';
import { AppButton } from 'wini-mobile-components';
import { ColorThemes } from '../../../assets/skin/colors';
import RecipientSelector from '../../../components/RecipientSelector';
import PointAmountInput from '../../../components/PointAmountInput';
import { TransactionType } from '../../../Config/Contanst';
import { Text } from 'react-native-paper';
import { DropdownForm } from '../../../modules/Default/form/component-form';
import { useForm, Controller } from 'react-hook-form';
import CustomDropdown from '../../../components/CustomDropdown';
import { TypoSkin } from '../../../assets/skin/typography';
import ShopDA from '../../../modules/shop/da';

interface Step1TransferInfoProps {
  currentPoints: number;
  transferAmount: string;
  recipientName?: string;
  recipientPhone?: string;
  onAmountChange: (amount: string) => void;
  onSelectRecipient: () => void;
  onNext: () => void;
  type: TransactionType;
}

interface FormData {
  accountNumber: string;
  recipientName: string;
  bank: { id: number; name: string } | null;
  amount: string;
}

const Step1TransferInfoMoney: React.FC<Step1TransferInfoProps> = ({
  currentPoints,
  transferAmount,
  recipientName,
  recipientPhone,
  onAmountChange,
  onSelectRecipient,
  onNext,
  type,
}) => {
  const [accountNumber, setAccountNumber] = React.useState('');
  const [name, setName] = React.useState('');
  const methods = useForm<FormData>({
    shouldFocusError: false,
    mode: 'onChange',
    defaultValues: {
      accountNumber: '',
      recipientName: '',
      bank: null,
      amount: transferAmount || ''
    }
  });

  const { control, formState: { errors, isValid }, watch, setValue, trigger } = methods;

  const formatInputValue = (value: string) => {
    // Remove all non-numeric characters
    const numericValue = value.replace(/[^0-9]/g, '');
    // Format with commas
    return numericValue.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  };

  const isValidAmount = () => {
    const amount = parseInt(transferAmount || '0');
    return amount > 0 && amount <= currentPoints;
  };

  const isFormValid = () => {
    const watchedValues = watch();
    console.log("check-watchedValues", watchedValues.bank)
    if (type === TransactionType.WithDrawMoney) {
      return watchedValues.accountNumber &&
        watchedValues.recipientName &&
        watchedValues.bank &&
        watchedValues.amount &&
        isValidAmount() &&
        !errors.accountNumber &&
        !errors.recipientName &&
        !errors.bank &&
        !errors.amount;
    }
    return false;
  };

  // Helper function để kiểm tra chi tiết validation
  const getValidationStatus = () => {
    const watchedValues = watch();
    return {
      fields: {
        accountNumber: {
          hasValue: !!watchedValues.accountNumber,
          hasError: !!errors.accountNumber,
          errorMessage: errors.accountNumber?.message
        },
        recipientName: {
          hasValue: !!watchedValues.recipientName,
          hasError: !!errors.recipientName,
          errorMessage: errors.recipientName?.message
        },
        bank: {
          hasValue: !!watchedValues.bank,
          hasError: !!errors.bank,
          errorMessage: errors.bank?.message
        },
        amount: {
          hasValue: !!watchedValues.amount,
          hasError: !!errors.amount,
          errorMessage: errors.amount?.message,
          isValidAmount: isValidAmount()
        }
      },
      isFormValid: isFormValid(),
      currentPoints,
      transferAmount: watchedValues.amount
    };
  };

  useEffect(() => {
    const watchedValues = watch();
    const validationDetails = {
      // Kiểm tra từng field
      hasAccountNumber: !!watchedValues.accountNumber,
      hasRecipientName: !!watchedValues.recipientName,
      hasBank: !!watchedValues.bank,
      hasAmount: !!watchedValues.amount,

      // Kiểm tra errors
      accountNumberError: !!errors.accountNumber,
      recipientNameError: !!errors.recipientName,
      bankError: !!errors.bank,
      amountError: !!errors.amount,

      // Kiểm tra amount hợp lệ
      isAmountValid: isValidAmount(),

      // Kết quả cuối cùng
      isFormValid: isFormValid()
    };

    console.log("=== VALIDATION STATUS ===");
    console.log("Form Values:", watchedValues);
    console.log("Validation Details:", validationDetails);
    console.log("Form Errors:", errors);
    console.log("========================");

  }, [watch().accountNumber, watch().recipientName, watch().bank, watch().amount])

  const formatNumber = (num: number) => {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }

  const handleAmountChange = (value: string) => {
    setValue('amount', value);
    onAmountChange(value);
    trigger('amount');
  };

  const validateAccountNumber = (value: string) => {
    if (!value || value.trim() === '') {
      return 'Số tài khoản không được để trống';
    }
    if (value.length < 6) {
      return 'Số tài khoản phải có ít nhất 6 ký tự';
    }
    if (!/^\d+$/.test(value)) {
      return 'Số tài khoản chỉ được chứa số';
    }
    return true;
  };

  const validateRecipientName = (value: string) => {
    if (!value || value.trim() === '') {
      return 'Tên người nhận không được để trống';
    }
    if (value.length < 2) {
      return 'Tên người nhận phải có ít nhất 2 ký tự';
    }
    if (!/^[a-zA-ZÀ-ỹ\s]+$/.test(value)) {
      return 'Tên người nhận chỉ được chứa chữ cái và khoảng trắng';
    }
    return true;
  };

  const validateAmount = (value: string) => {
    const numericValue = value.replace(/[^0-9]/g, '');
    const amount = parseInt(numericValue || '0');

    if (!value || value.trim() === '') {
      return 'Số tiền không được để trống';
    }
    if (amount <= 0) {
      return 'Số tiền phải lớn hơn 0';
    }
    if (amount > currentPoints) {
      return `Số tiền không được vượt quá ${formatNumber(currentPoints)} point`;
    }
    return true;
  };

  return (
    <View style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Số tài khoản</Text>
          <Controller
            control={control}
            name="accountNumber"
            rules={{ validate: validateAccountNumber }}
            render={({ field: { onChange, onBlur, value } }) => (
              <TextInput
                style={[styles.input, errors.accountNumber && styles.inputError]}
                onBlur={onBlur}
                onChangeText={onChange}
                value={value}
                keyboardType="numeric"
                placeholder="Nhập số tài khoản"
              />
            )}
          />
          {errors.accountNumber && (
            <Text style={styles.errorText}>{errors.accountNumber.message}</Text>
          )}
        </View>

        <View style={styles.inputContainer}>
          <Text style={styles.label}>Tên người nhận</Text>
          <Controller
            control={control}
            name="recipientName"
            rules={{ validate: validateRecipientName }}
            render={({ field: { onChange, onBlur, value } }) => (
              <TextInput
                style={[styles.input, errors.recipientName && styles.inputError]}
                onBlur={onBlur}
                onChangeText={onChange}
                value={value}
                keyboardType="default"
                placeholder="Nhập tên người nhận"
              />
            )}
          />
          {errors.recipientName && (
            <Text style={styles.errorText}>{errors.recipientName.message}</Text>
          )}
        </View>

        <View style={styles.inputContainer}>
          <Text style={styles.label}>Ngân hàng</Text>
          <DropdownForm
            placeholder="Chọn ngân hàng"
            name="bank"
            control={methods.control as any}
            style={{
              borderColor: errors.bank ? '#FF4444' : '#D4E2F3',
              height: 50,
              fontSize: 20,
              color: '#000',
            }}
            errors={methods.formState.errors}
            options={[
              { id: 1, name: 'Ngân hàng Vietcombank' },
              { id: 2, name: 'Ngân hàng Techcombank' },
              { id: 3, name: 'Ngân hàng Agribank' },
              { id: 4, name: 'Ngân hàng Vietinbank' },
              { id: 5, name: 'Ngân hàng BIDV' },
              { id: 6, name: 'Ngân hàng MB' },
              { id: 7, name: 'Ngân hàng Viet Capital Bank' },
              { id: 8, name: 'Ngân hàng Vietcom Bank' },
            ]}
          />
          {errors.bank && (
            <Text style={styles.errorText}>{errors.bank.message}</Text>
          )}
        </View>

        <View style={styles.container}>
          <Text style={styles.label}>Point {type === TransactionType.tranfer ? 'chuyển' : 'rút'}</Text>

          <View style={styles.inputContainer}>
            <Controller
              control={control}
              name="amount"
              rules={{ validate: validateAmount }}
              render={({ field: { onChange, value } }) => (
                <TextInput
                  value={formatInputValue(value)}
                  onChangeText={(text) => {
                    const numericValue = text.replace(/[^0-9]/g, '');
                    onChange(numericValue);
                    handleAmountChange(numericValue);
                  }}
                  placeholder="0"
                  keyboardType="numeric"
                  style={[styles.input, errors.amount && styles.inputError]}
                />
              )}
            />
            <TouchableOpacity
              style={{
                position: 'absolute',
                right: 12,
                top: '75%',
                transform: [{ translateY: -12 }],
                backgroundColor: '#FFC043',
                paddingHorizontal: 8,
                paddingVertical: 4,
                borderRadius: 12,
              }}
              onPress={() => {
                setValue('amount', currentPoints.toString());
                handleAmountChange(currentPoints.toString());
              }}
            >
              <Text style={{
                ...TypoSkin.label4,
                color: '#DA251D',
                fontWeight: '600',
                fontSize: 12,
              }}>MAX</Text>
            </TouchableOpacity>
          </View>
          {errors.amount && (
            <Text style={styles.errorText}>{errors.amount.message}</Text>
          )}

          <View style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            paddingTop: 16,
            borderTopWidth: 1,
            borderTopColor: ColorThemes.light.neutral_lighter_border_color,
          }}>
            <Text style={{
              ...TypoSkin.regular2,
              color: ColorThemes.light.neutral_text_subtitle_color,
              fontSize: 14,
            }}>Số dư bán hàng</Text>
            <Text style={{
              ...TypoSkin.heading6,
              color: ColorThemes.light.primary_main_color,
            }}>{formatNumber(currentPoints ? currentPoints : 0)}</Text>
          </View>
        </View>
      </ScrollView>

      <View style={styles.buttonContainer}>
        <AppButton
          title="Tiếp theo"
          onPress={onNext}
          disabled={!isFormValid()}
          backgroundColor={
            isFormValid()
              ? ColorThemes.light.primary_main_color
              : ColorThemes.light.neutral_lighter_border_color
          }
          containerStyle={styles.button}
          borderColor='transparent'
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  inputContainer: {
    paddingBottom: 16,
    paddingTop: 16,
  },
  label: {
    marginBottom: 8,
    fontSize: 14,
    color: '#666',
  },
  input: {
    backgroundColor: 'white',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#D4E2F3',
    height: 50,
    paddingHorizontal: 16,
    fontSize: 13,
    fontFamily: 'roboto',
    color: 'black',
  },
  inputError: {
    borderColor: '#FF4444',
  },
  errorText: {
    color: '#FF4444',
    fontSize: 12,
    marginTop: 4,
    fontFamily: 'roboto',
  },
  buttonContainer: {
    padding: 16,
    backgroundColor: ColorThemes.light.white,
    borderTopWidth: 1,
    borderTopColor: ColorThemes.light.neutral_lighter_border_color,
  },
  button: {
    borderRadius: 24,
    height: 48,
    width: '80%',
    margin: 'auto',
  },
});

export default Step1TransferInfoMoney
